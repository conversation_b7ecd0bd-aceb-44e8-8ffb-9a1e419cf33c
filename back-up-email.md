# Tutorial: Respaldos Automatizados y Notificaciones por Email para Moodle 4.1.15+

## Tabla de Contenidos
1. [Introducción](#introducción)
2. [Configuración de Respaldos Automatizados](#configuración-de-respaldos-automatizados)
3. [Integración con Email](#integración-con-email)
4. [Integración con Almacenamiento en la Nube](#integración-con-almacenamiento-en-la-nube)
5. [Mejores Prácticas](#mejores-prácticas)
6. [Organización Sistemática](#organización-sistemática)
7. [Monitoreo y Registro de Logs](#monitoreo-y-registro-de-logs)
8. [Pruebas de Recuperación](#pruebas-de-recuperación)

## Introducción

Este tutorial proporciona una guía completa para implementar soluciones de respaldo automatizadas para Moodle 4.1.15+ en entornos de intranet, incluyendo notificaciones por email y almacenamiento en la nube.

### Requisitos Previos
- Moodle 4.1.15 o superior
- Acceso de administrador al servidor
- Acceso SSH al servidor
- Configuración de cron habilitada
- Servidor de correo configurado

## Configuración de Respaldos Automatizados

### 1. Configuración Nativa de Moodle

#### Paso 1: Configurar Respaldos Automatizados en la Interfaz Web

1. Accede como administrador a tu sitio Moodle
2. Ve a **Administración del sitio > Cursos > Respaldos > Configuración automatizada de respaldos**
3. Configura los siguientes parámetros:

```
- Respaldo automatizado activo: Sí
- Ejecutar en: Selecciona días y hora (recomendado: madrugada)
- Guardar en: /ruta/a/respaldos/moodle/
- Mantener: 7 días (ajustar según necesidades)
- Incluir usuarios: Sí
- Incluir archivos de usuarios: Sí
- Incluir actividades: Sí
```

#### Paso 2: Script de Respaldo Personalizado

Crea un script personalizado para mayor control:

```bash
#!/bin/bash
# /opt/moodle-backup/backup-moodle.sh

# Configuración
MOODLE_PATH="/var/www/html/moodle"
BACKUP_DIR="/opt/moodle-backup/backups"
DB_NAME="moodle"
DB_USER="moodle_user"
DB_PASS="tu_password"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_NAME="moodle_backup_${DATE}"

# Crear directorio de respaldo si no existe
mkdir -p "$BACKUP_DIR"

# Función de logging
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$BACKUP_DIR/backup.log"
}

log_message "Iniciando respaldo de Moodle..."

# Respaldo de la base de datos
log_message "Respaldando base de datos..."
mysqldump -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" > "$BACKUP_DIR/${BACKUP_NAME}_db.sql"

if [ $? -eq 0 ]; then
    log_message "Respaldo de base de datos completado exitosamente"
else
    log_message "ERROR: Fallo en el respaldo de la base de datos"
    exit 1
fi

# Respaldo de archivos de Moodle
log_message "Respaldando archivos de Moodle..."
tar -czf "$BACKUP_DIR/${BACKUP_NAME}_files.tar.gz" -C "$MOODLE_PATH" .

if [ $? -eq 0 ]; then
    log_message "Respaldo de archivos completado exitosamente"
else
    log_message "ERROR: Fallo en el respaldo de archivos"
    exit 1
fi

# Respaldo de moodledata
log_message "Respaldando moodledata..."
tar -czf "$BACKUP_DIR/${BACKUP_NAME}_data.tar.gz" -C "/var/moodledata" .

if [ $? -eq 0 ]; then
    log_message "Respaldo de moodledata completado exitosamente"
else
    log_message "ERROR: Fallo en el respaldo de moodledata"
    exit 1
fi

log_message "Respaldo completado: $BACKUP_NAME"
```

### 2. Configuración de Cron

Agrega la siguiente línea al crontab para ejecutar respaldos diarios a las 2:00 AM:

```bash
# Editar crontab
sudo crontab -e

# Agregar línea
0 2 * * * /opt/moodle-backup/backup-moodle.sh
```

## Integración con Email

### 1. Script de Notificación por Email

Crea un script para enviar notificaciones:

```bash
#!/bin/bash
# /opt/moodle-backup/send-notification.sh

BACKUP_DIR="/opt/moodle-backup/backups"
EMAIL_TO="<EMAIL>"
EMAIL_FROM="<EMAIL>"
SUBJECT="Reporte de Respaldo Moodle - $(date +%Y-%m-%d)"

# Función para enviar email
send_email() {
    local status=$1
    local message=$2
    local attachment=$3
    
    if [ "$status" = "success" ]; then
        SUBJECT="✅ $SUBJECT - EXITOSO"
    else
        SUBJECT="❌ $SUBJECT - ERROR"
    fi
    
    # Crear cuerpo del email
    cat > /tmp/email_body.txt << EOF
Estimado Administrador,

Reporte de respaldo automatizado de Moodle:

Estado: $status
Fecha y Hora: $(date)
Mensaje: $message

Detalles del sistema:
- Servidor: $(hostname)
- Espacio disponible: $(df -h $BACKUP_DIR | tail -1 | awk '{print $4}')
- Últimos respaldos:
$(ls -lht $BACKUP_DIR/*.tar.gz 2>/dev/null | head -5)

Saludos,
Sistema de Respaldos Automatizados
EOF

    # Enviar email con o sin adjunto
    if [ -n "$attachment" ] && [ -f "$attachment" ]; then
        mutt -s "$SUBJECT" -a "$attachment" -- "$EMAIL_TO" < /tmp/email_body.txt
    else
        mutt -s "$SUBJECT" "$EMAIL_TO" < /tmp/email_body.txt
    fi
    
    rm -f /tmp/email_body.txt
}

# Verificar último respaldo
LATEST_BACKUP=$(ls -t $BACKUP_DIR/*.tar.gz 2>/dev/null | head -1)
if [ -n "$LATEST_BACKUP" ]; then
    send_email "success" "Respaldo completado exitosamente" "$BACKUP_DIR/backup.log"
else
    send_email "error" "No se encontraron archivos de respaldo recientes"
fi
```

### 2. Configuración de Postfix/Sendmail

Para sistemas Ubuntu/Debian:

```bash
# Instalar postfix
sudo apt update
sudo apt install postfix mutt

# Configurar postfix para relay SMTP
sudo nano /etc/postfix/main.cf
```

Agregar configuración SMTP:

```
relayhost = [smtp.gmail.com]:587
smtp_use_tls = yes
smtp_sasl_auth_enable = yes
smtp_sasl_password_maps = hash:/etc/postfix/sasl_passwd
smtp_sasl_security_options = noanonymous
```

Crear archivo de credenciales:

```bash
# Crear archivo de credenciales
sudo nano /etc/postfix/sasl_passwd

# Agregar línea (reemplazar con tus credenciales)
[smtp.gmail.com]:587 <EMAIL>:tu_password_app

# Generar hash y reiniciar
sudo postmap /etc/postfix/sasl_passwd
sudo systemctl restart postfix
```

## Integración con Almacenamiento en la Nube

### 1. Google Drive con rclone

#### Instalación y Configuración

```bash
# Instalar rclone
curl https://rclone.org/install.sh | sudo bash

# Configurar Google Drive
rclone config

# Seguir el asistente para configurar Google Drive
# Nombre: gdrive
# Tipo: drive
# Autorizar con tu cuenta de Google
```

#### Script de Subida a Google Drive

```bash
#!/bin/bash
# /opt/moodle-backup/upload-gdrive.sh

BACKUP_DIR="/opt/moodle-backup/backups"
GDRIVE_FOLDER="Moodle-Backups"
DATE=$(date +%Y%m%d)

# Función de logging
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$BACKUP_DIR/upload.log"
}

# Crear carpeta en Google Drive si no existe
rclone mkdir gdrive:$GDRIVE_FOLDER

# Subir archivos del día actual
log_message "Iniciando subida a Google Drive..."

for file in $BACKUP_DIR/*${DATE}*.tar.gz $BACKUP_DIR/*${DATE}*.sql; do
    if [ -f "$file" ]; then
        filename=$(basename "$file")
        log_message "Subiendo: $filename"

        rclone copy "$file" gdrive:$GDRIVE_FOLDER/

        if [ $? -eq 0 ]; then
            log_message "✅ Subida exitosa: $filename"
        else
            log_message "❌ Error subiendo: $filename"
        fi
    fi
done

# Limpiar archivos antiguos en Google Drive (mantener 30 días)
log_message "Limpiando archivos antiguos en Google Drive..."
rclone delete gdrive:$GDRIVE_FOLDER/ --min-age 30d

log_message "Proceso de subida completado"
```

### 2. AWS S3

#### Configuración de AWS CLI

```bash
# Instalar AWS CLI
sudo apt install awscli

# Configurar credenciales
aws configure
# AWS Access Key ID: tu_access_key
# AWS Secret Access Key: tu_secret_key
# Default region: us-east-1
# Default output format: json
```

#### Script de Subida a S3

```bash
#!/bin/bash
# /opt/moodle-backup/upload-s3.sh

BACKUP_DIR="/opt/moodle-backup/backups"
S3_BUCKET="tu-bucket-moodle-backups"
DATE=$(date +%Y%m%d)

# Función de logging
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$BACKUP_DIR/s3-upload.log"
}

log_message "Iniciando subida a S3..."

# Subir archivos del día actual
for file in $BACKUP_DIR/*${DATE}*.tar.gz $BACKUP_DIR/*${DATE}*.sql; do
    if [ -f "$file" ]; then
        filename=$(basename "$file")
        log_message "Subiendo a S3: $filename"

        aws s3 cp "$file" s3://$S3_BUCKET/moodle-backups/$(date +%Y)/$(date +%m)/

        if [ $? -eq 0 ]; then
            log_message "✅ Subida exitosa a S3: $filename"
        else
            log_message "❌ Error subiendo a S3: $filename"
        fi
    fi
done

# Configurar lifecycle policy para eliminar archivos antiguos
aws s3api put-bucket-lifecycle-configuration \
    --bucket $S3_BUCKET \
    --lifecycle-configuration file:///opt/moodle-backup/s3-lifecycle.json

log_message "Proceso de subida a S3 completado"
```

Crear archivo de configuración de lifecycle:

```json
{
    "Rules": [
        {
            "ID": "MoodleBackupRetention",
            "Status": "Enabled",
            "Filter": {
                "Prefix": "moodle-backups/"
            },
            "Transitions": [
                {
                    "Days": 30,
                    "StorageClass": "STANDARD_IA"
                },
                {
                    "Days": 90,
                    "StorageClass": "GLACIER"
                }
            ],
            "Expiration": {
                "Days": 365
            }
        }
    ]
}
```

## Mejores Prácticas

### 1. Consideraciones de Seguridad

#### Cifrado de Respaldos

```bash
#!/bin/bash
# Función para cifrar respaldos
encrypt_backup() {
    local file=$1
    local password="tu_password_seguro"

    # Cifrar con GPG
    gpg --batch --yes --passphrase="$password" --cipher-algo AES256 --compress-algo 1 --symmetric "$file"

    # Eliminar archivo original
    rm "$file"

    echo "${file}.gpg"
}

# Uso en el script principal
ENCRYPTED_FILE=$(encrypt_backup "$BACKUP_DIR/${BACKUP_NAME}_db.sql")
```

#### Permisos de Archivos

```bash
# Configurar permisos seguros
chmod 700 /opt/moodle-backup/
chmod 600 /opt/moodle-backup/*.sh
chmod 600 /opt/moodle-backup/backups/*

# Crear usuario específico para respaldos
sudo useradd -r -s /bin/bash moodle-backup
sudo chown -R moodle-backup:moodle-backup /opt/moodle-backup/
```

### 2. Programación de Respaldos

#### Estrategia 3-2-1
- **3** copias de los datos
- **2** medios de almacenamiento diferentes
- **1** copia offsite (nube)

#### Cronograma Recomendado

```bash
# Respaldos completos diarios
0 2 * * * /opt/moodle-backup/backup-moodle.sh

# Respaldos incrementales cada 6 horas
0 */6 * * * /opt/moodle-backup/incremental-backup.sh

# Subida a la nube diaria
30 3 * * * /opt/moodle-backup/upload-gdrive.sh

# Limpieza semanal
0 4 * * 0 /opt/moodle-backup/cleanup.sh

# Verificación de integridad semanal
0 5 * * 0 /opt/moodle-backup/verify-backups.sh
```

### 3. Políticas de Retención

```bash
#!/bin/bash
# /opt/moodle-backup/cleanup.sh

BACKUP_DIR="/opt/moodle-backup/backups"

# Eliminar respaldos locales mayores a 7 días
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete

# Mantener respaldos semanales por 1 mes
find $BACKUP_DIR -name "*_weekly_*" -mtime +30 -delete

# Mantener respaldos mensuales por 1 año
find $BACKUP_DIR -name "*_monthly_*" -mtime +365 -delete
```

## Organización Sistemática

### 1. Estructura de Directorios

```
/opt/moodle-backup/
├── backups/
│   ├── daily/
│   ├── weekly/
│   ├── monthly/
│   └── emergency/
├── scripts/
│   ├── backup-moodle.sh
│   ├── send-notification.sh
│   ├── upload-gdrive.sh
│   └── verify-backups.sh
├── config/
│   ├── backup.conf
│   └── email.conf
└── logs/
    ├── backup.log
    ├── upload.log
    └── error.log
```

### 2. Nomenclatura de Archivos

```bash
# Formato de nombres de archivo
BACKUP_NAME="moodle_${BACKUP_TYPE}_${DATE}_${TIME}"

# Ejemplos:
# moodle_daily_20241203_020000_db.sql
# moodle_weekly_20241203_020000_files.tar.gz
# moodle_monthly_20241203_020000_complete.tar.gz
# moodle_emergency_20241203_143000_db.sql
```

### 3. Script de Organización

```bash
#!/bin/bash
# /opt/moodle-backup/organize-backups.sh

BACKUP_DIR="/opt/moodle-backup/backups"
DATE=$(date +%Y%m%d)
DAY_OF_WEEK=$(date +%u)  # 1=Monday, 7=Sunday
DAY_OF_MONTH=$(date +%d)

# Función para mover archivos según el tipo
organize_backups() {
    local source_dir="$BACKUP_DIR"

    # Crear subdirectorios si no existen
    mkdir -p "$BACKUP_DIR"/{daily,weekly,monthly,emergency}

    # Mover respaldos diarios
    mv "$source_dir"/moodle_backup_${DATE}_*.* "$BACKUP_DIR/daily/" 2>/dev/null

    # Si es domingo, crear respaldo semanal
    if [ "$DAY_OF_WEEK" -eq 7 ]; then
        cp "$BACKUP_DIR/daily"/moodle_backup_${DATE}_*.* "$BACKUP_DIR/weekly/" 2>/dev/null
    fi

    # Si es día 1 del mes, crear respaldo mensual
    if [ "$DAY_OF_MONTH" -eq 1 ]; then
        cp "$BACKUP_DIR/daily"/moodle_backup_${DATE}_*.* "$BACKUP_DIR/monthly/" 2>/dev/null
    fi
}

organize_backups
```

## Monitoreo y Registro de Logs

### 1. Sistema de Logging Avanzado

```bash
#!/bin/bash
# /opt/moodle-backup/logging.sh

LOG_DIR="/opt/moodle-backup/logs"
LOG_FILE="$LOG_DIR/backup_$(date +%Y%m).log"
ERROR_LOG="$LOG_DIR/error.log"

# Función de logging con niveles
log_with_level() {
    local level=$1
    local message=$2
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')

    case $level in
        "INFO")
            echo "[$timestamp] [INFO] $message" | tee -a "$LOG_FILE"
            ;;
        "WARN")
            echo "[$timestamp] [WARN] $message" | tee -a "$LOG_FILE"
            ;;
        "ERROR")
            echo "[$timestamp] [ERROR] $message" | tee -a "$LOG_FILE" "$ERROR_LOG"
            ;;
        "SUCCESS")
            echo "[$timestamp] [SUCCESS] $message" | tee -a "$LOG_FILE"
            ;;
    esac
}

# Función para generar reporte de estado
generate_status_report() {
    local report_file="$LOG_DIR/status_report_$(date +%Y%m%d).txt"

    cat > "$report_file" << EOF
=== REPORTE DE ESTADO DE RESPALDOS ===
Fecha: $(date)
Servidor: $(hostname)

=== ESTADÍSTICAS DE RESPALDOS ===
Respaldos completados hoy: $(grep -c "SUCCESS.*completado" "$LOG_FILE")
Errores registrados hoy: $(grep -c "ERROR" "$LOG_FILE")
Último respaldo exitoso: $(grep "SUCCESS.*completado" "$LOG_FILE" | tail -1)

=== ESPACIO EN DISCO ===
$(df -h /opt/moodle-backup/)

=== ARCHIVOS DE RESPALDO RECIENTES ===
$(ls -lht /opt/moodle-backup/backups/daily/ | head -10)

=== ERRORES RECIENTES ===
$(tail -20 "$ERROR_LOG")
EOF

    echo "$report_file"
}
```

### 2. Monitoreo con Nagios/Zabbix

#### Script de Verificación para Nagios

```bash
#!/bin/bash
# /opt/moodle-backup/check-backup-status.sh

BACKUP_DIR="/opt/moodle-backup/backups/daily"
CRITICAL_HOURS=26  # Crítico si no hay respaldo en 26 horas
WARNING_HOURS=25   # Advertencia si no hay respaldo en 25 horas

# Encontrar el respaldo más reciente
LATEST_BACKUP=$(find "$BACKUP_DIR" -name "*.tar.gz" -type f -printf '%T@ %p\n' | sort -n | tail -1 | cut -d' ' -f2-)

if [ -z "$LATEST_BACKUP" ]; then
    echo "CRITICAL - No se encontraron archivos de respaldo"
    exit 2
fi

# Calcular antigüedad del respaldo
BACKUP_TIME=$(stat -c %Y "$LATEST_BACKUP")
CURRENT_TIME=$(date +%s)
HOURS_OLD=$(( (CURRENT_TIME - BACKUP_TIME) / 3600 ))

if [ $HOURS_OLD -gt $CRITICAL_HOURS ]; then
    echo "CRITICAL - Último respaldo tiene $HOURS_OLD horas de antigüedad"
    exit 2
elif [ $HOURS_OLD -gt $WARNING_HOURS ]; then
    echo "WARNING - Último respaldo tiene $HOURS_OLD horas de antigüedad"
    exit 1
else
    echo "OK - Último respaldo tiene $HOURS_OLD horas de antigüedad"
    exit 0
fi
```

### 3. Dashboard de Monitoreo

```bash
#!/bin/bash
# /opt/moodle-backup/dashboard.sh

# Generar página HTML de estado
generate_dashboard() {
    local html_file="/var/www/html/backup-status.html"

    cat > "$html_file" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>Estado de Respaldos Moodle</title>
    <meta charset="UTF-8">
    <meta http-equiv="refresh" content="300">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .status-ok { color: green; }
        .status-warning { color: orange; }
        .status-error { color: red; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>Estado de Respaldos Moodle</h1>
    <p>Última actualización: $(date)</p>

    <h2>Resumen</h2>
    <table>
        <tr><th>Métrica</th><th>Valor</th><th>Estado</th></tr>
        <tr><td>Último respaldo</td><td>$(ls -t /opt/moodle-backup/backups/daily/*.tar.gz | head -1 | xargs stat -c %y)</td><td class="status-ok">✓</td></tr>
        <tr><td>Espacio disponible</td><td>$(df -h /opt/moodle-backup/ | tail -1 | awk '{print $4}')</td><td class="status-ok">✓</td></tr>
        <tr><td>Respaldos hoy</td><td>$(find /opt/moodle-backup/backups/daily/ -name "*$(date +%Y%m%d)*" | wc -l)</td><td class="status-ok">✓</td></tr>
    </table>

    <h2>Archivos de Respaldo Recientes</h2>
    <table>
        <tr><th>Archivo</th><th>Tamaño</th><th>Fecha</th></tr>
$(ls -lht /opt/moodle-backup/backups/daily/*.tar.gz | head -10 | awk '{print "<tr><td>"$9"</td><td>"$5"</td><td>"$6" "$7" "$8"</td></tr>"}')
    </table>
</body>
</html>
EOF
}

generate_dashboard
```

## Pruebas de Recuperación

### 1. Script de Verificación de Integridad

```bash
#!/bin/bash
# /opt/moodle-backup/verify-backups.sh

BACKUP_DIR="/opt/moodle-backup/backups"
TEST_DIR="/tmp/moodle-backup-test"
LOG_FILE="/opt/moodle-backup/logs/verification.log"

# Función de logging
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# Función para verificar integridad de archivos comprimidos
verify_archive_integrity() {
    local archive_file=$1

    log_message "Verificando integridad de: $(basename $archive_file)"

    # Verificar archivo tar.gz
    if [[ "$archive_file" == *.tar.gz ]]; then
        if tar -tzf "$archive_file" >/dev/null 2>&1; then
            log_message "✅ Archivo íntegro: $(basename $archive_file)"
            return 0
        else
            log_message "❌ Archivo corrupto: $(basename $archive_file)"
            return 1
        fi
    fi

    # Verificar archivo SQL
    if [[ "$archive_file" == *.sql ]]; then
        if head -1 "$archive_file" | grep -q "MySQL dump"; then
            log_message "✅ Archivo SQL válido: $(basename $archive_file)"
            return 0
        else
            log_message "❌ Archivo SQL inválido: $(basename $archive_file)"
            return 1
        fi
    fi
}

# Función para prueba de restauración completa
test_full_restoration() {
    local db_backup=$1
    local files_backup=$2
    local data_backup=$3

    log_message "Iniciando prueba de restauración completa..."

    # Crear directorio de prueba
    mkdir -p "$TEST_DIR"
    cd "$TEST_DIR"

    # Extraer archivos de Moodle
    log_message "Extrayendo archivos de Moodle..."
    tar -xzf "$files_backup" -C "$TEST_DIR/moodle/"

    if [ $? -eq 0 ]; then
        log_message "✅ Extracción de archivos exitosa"
    else
        log_message "❌ Error en extracción de archivos"
        return 1
    fi

    # Extraer moodledata
    log_message "Extrayendo moodledata..."
    mkdir -p "$TEST_DIR/moodledata"
    tar -xzf "$data_backup" -C "$TEST_DIR/moodledata/"

    if [ $? -eq 0 ]; then
        log_message "✅ Extracción de moodledata exitosa"
    else
        log_message "❌ Error en extracción de moodledata"
        return 1
    fi

    # Crear base de datos de prueba
    log_message "Creando base de datos de prueba..."
    mysql -u root -p"$DB_ROOT_PASS" -e "CREATE DATABASE moodle_test;"
    mysql -u root -p"$DB_ROOT_PASS" moodle_test < "$db_backup"

    if [ $? -eq 0 ]; then
        log_message "✅ Restauración de base de datos exitosa"
    else
        log_message "❌ Error en restauración de base de datos"
        return 1
    fi

    # Verificar estructura de la base de datos
    TABLES_COUNT=$(mysql -u root -p"$DB_ROOT_PASS" moodle_test -e "SHOW TABLES;" | wc -l)
    log_message "Tablas restauradas: $TABLES_COUNT"

    # Limpiar
    mysql -u root -p"$DB_ROOT_PASS" -e "DROP DATABASE moodle_test;"
    rm -rf "$TEST_DIR"

    log_message "✅ Prueba de restauración completada exitosamente"
    return 0
}

# Función principal de verificación
main_verification() {
    log_message "=== INICIANDO VERIFICACIÓN DE RESPALDOS ==="

    # Verificar respaldos del día anterior
    YESTERDAY=$(date -d "yesterday" +%Y%m%d)

    # Buscar archivos del día anterior
    DB_FILE=$(find "$BACKUP_DIR" -name "*${YESTERDAY}*_db.sql" | head -1)
    FILES_BACKUP=$(find "$BACKUP_DIR" -name "*${YESTERDAY}*_files.tar.gz" | head -1)
    DATA_BACKUP=$(find "$BACKUP_DIR" -name "*${YESTERDAY}*_data.tar.gz" | head -1)

    if [ -z "$DB_FILE" ] || [ -z "$FILES_BACKUP" ] || [ -z "$DATA_BACKUP" ]; then
        log_message "❌ No se encontraron todos los archivos de respaldo del día anterior"
        return 1
    fi

    # Verificar integridad de cada archivo
    verify_archive_integrity "$DB_FILE"
    verify_archive_integrity "$FILES_BACKUP"
    verify_archive_integrity "$DATA_BACKUP"

    # Realizar prueba de restauración (opcional, comentar si no se desea)
    # test_full_restoration "$DB_FILE" "$FILES_BACKUP" "$DATA_BACKUP"

    log_message "=== VERIFICACIÓN COMPLETADA ==="
}

# Ejecutar verificación
main_verification
```

### 2. Procedimiento de Recuperación de Emergencia

```bash
#!/bin/bash
# /opt/moodle-backup/emergency-restore.sh

# ADVERTENCIA: Este script debe ejecutarse con extrema precaución
# Siempre hacer respaldo del estado actual antes de restaurar

BACKUP_DIR="/opt/moodle-backup/backups"
MOODLE_PATH="/var/www/html/moodle"
MOODLEDATA_PATH="/var/moodledata"
DB_NAME="moodle"
DB_USER="moodle_user"
DB_PASS="tu_password"

# Función de confirmación
confirm_action() {
    echo "⚠️  ADVERTENCIA: Esta operación sobrescribirá la instalación actual de Moodle"
    echo "¿Está seguro de que desea continuar? (escriba 'SI' para confirmar)"
    read -r confirmation

    if [ "$confirmation" != "SI" ]; then
        echo "Operación cancelada"
        exit 1
    fi
}

# Función de restauración
restore_from_backup() {
    local backup_date=$1

    echo "Buscando respaldos para la fecha: $backup_date"

    # Buscar archivos de respaldo
    DB_FILE=$(find "$BACKUP_DIR" -name "*${backup_date}*_db.sql" | head -1)
    FILES_BACKUP=$(find "$BACKUP_DIR" -name "*${backup_date}*_files.tar.gz" | head -1)
    DATA_BACKUP=$(find "$BACKUP_DIR" -name "*${backup_date}*_data.tar.gz" | head -1)

    if [ -z "$DB_FILE" ] || [ -z "$FILES_BACKUP" ] || [ -z "$DATA_BACKUP" ]; then
        echo "❌ No se encontraron todos los archivos necesarios para la fecha $backup_date"
        exit 1
    fi

    echo "Archivos encontrados:"
    echo "- Base de datos: $(basename $DB_FILE)"
    echo "- Archivos: $(basename $FILES_BACKUP)"
    echo "- Datos: $(basename $DATA_BACKUP)"

    confirm_action

    # Crear respaldo de emergencia del estado actual
    echo "Creando respaldo de emergencia del estado actual..."
    EMERGENCY_DIR="/opt/moodle-backup/backups/emergency/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$EMERGENCY_DIR"

    mysqldump -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" > "$EMERGENCY_DIR/current_db.sql"
    tar -czf "$EMERGENCY_DIR/current_files.tar.gz" -C "$MOODLE_PATH" .
    tar -czf "$EMERGENCY_DIR/current_data.tar.gz" -C "$MOODLEDATA_PATH" .

    # Poner Moodle en modo mantenimiento
    echo "Activando modo mantenimiento..."
    sudo -u www-data php "$MOODLE_PATH/admin/cli/maintenance.php" --enable

    # Restaurar base de datos
    echo "Restaurando base de datos..."
    mysql -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" < "$DB_FILE"

    # Restaurar archivos
    echo "Restaurando archivos de Moodle..."
    rm -rf "$MOODLE_PATH"/*
    tar -xzf "$FILES_BACKUP" -C "$MOODLE_PATH/"
    chown -R www-data:www-data "$MOODLE_PATH"

    # Restaurar moodledata
    echo "Restaurando moodledata..."
    rm -rf "$MOODLEDATA_PATH"/*
    tar -xzf "$DATA_BACKUP" -C "$MOODLEDATA_PATH/"
    chown -R www-data:www-data "$MOODLEDATA_PATH"

    # Desactivar modo mantenimiento
    echo "Desactivando modo mantenimiento..."
    sudo -u www-data php "$MOODLE_PATH/admin/cli/maintenance.php" --disable

    echo "✅ Restauración completada exitosamente"
    echo "Respaldo de emergencia guardado en: $EMERGENCY_DIR"
}

# Verificar parámetros
if [ $# -eq 0 ]; then
    echo "Uso: $0 <fecha_respaldo>"
    echo "Ejemplo: $0 20241203"
    echo ""
    echo "Respaldos disponibles:"
    ls -1 "$BACKUP_DIR"/*_db.sql | sed 's/.*moodle_backup_\([0-9]*\)_.*/\1/' | sort -u
    exit 1
fi

restore_from_backup $1
```

### 3. Lista de Verificación para Pruebas

#### Checklist Mensual de Verificación

```markdown
## Lista de Verificación Mensual - Respaldos Moodle

### Verificaciones Automáticas
- [ ] Ejecutar script de verificación de integridad
- [ ] Revisar logs de respaldos del último mes
- [ ] Verificar espacio disponible en disco
- [ ] Comprobar funcionamiento de notificaciones por email
- [ ] Verificar subidas a almacenamiento en la nube

### Verificaciones Manuales
- [ ] Descargar un respaldo aleatorio de la nube
- [ ] Verificar que los archivos se pueden extraer correctamente
- [ ] Comprobar que la base de datos se puede importar
- [ ] Revisar configuración de retención de archivos
- [ ] Actualizar documentación si es necesario

### Pruebas de Restauración (Trimestral)
- [ ] Preparar entorno de pruebas
- [ ] Restaurar respaldo completo en entorno de pruebas
- [ ] Verificar funcionalidad básica de Moodle
- [ ] Probar acceso de usuarios
- [ ] Verificar integridad de cursos y contenido
- [ ] Documentar tiempo de restauración
- [ ] Limpiar entorno de pruebas

### Acciones Correctivas
- [ ] Resolver problemas identificados
- [ ] Actualizar scripts si es necesario
- [ ] Mejorar documentación
- [ ] Notificar a stakeholders sobre el estado
```

## Conclusión

Este tutorial proporciona una solución completa para implementar respaldos automatizados de Moodle con las siguientes características:

- **Automatización completa** con scripts personalizables
- **Notificaciones por email** para monitoreo proactivo
- **Integración con la nube** para almacenamiento offsite
- **Mejores prácticas de seguridad** y retención
- **Monitoreo y logging** comprehensivo
- **Procedimientos de recuperación** probados

### Próximos Pasos

1. Adaptar los scripts a tu entorno específico
2. Configurar las credenciales y rutas apropiadas
3. Probar en un entorno de desarrollo primero
4. Implementar gradualmente en producción
5. Establecer un cronograma de verificaciones regulares

### Soporte y Mantenimiento

- Revisar logs regularmente
- Actualizar scripts según cambios en Moodle
- Mantener credenciales de acceso seguras
- Documentar cualquier modificación realizada

---

**Nota**: Este tutorial está diseñado para Moodle 4.1.15+ en entornos Linux. Adapte los comandos y rutas según su configuración específica.
