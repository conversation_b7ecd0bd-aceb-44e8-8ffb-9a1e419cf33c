<?php
// Código para bloquear acceso móvil en Moodle
// Agregar este código al final del archivo config.php (antes del ?>)

// Función para detectar dispositivos móviles
function is_mobile_device() {
    // User agents de dispositivos móviles (teléfonos)
    $mobile_agents = array(
        'Mobile', 'Android.*Mobile', 'iPhone', 'iPod', 'BlackBerry', 
        'Windows Phone', 'Opera Mini', 'IEMobile', 'webOS', 'Palm'
    );
    
    // User agents de tablets (permitidos)
    $tablet_agents = array('iPad', 'Tablet', 'Surface');
    
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    
    // Permitir tablets
    foreach ($tablet_agents as $tablet) {
        if (stripos($user_agent, $tablet) !== false) {
            return false;
        }
    }
    
    // Permitir Android tablets (Android sin "Mobile")
    if (stripos($user_agent, 'Android') !== false && stripos($user_agent, 'Mobile') === false) {
        return false;
    }
    
    // Bloquear móviles
    foreach ($mobile_agents as $mobile) {
        if (preg_match('/' . $mobile . '/i', $user_agent)) {
            return true;
        }
    }
    
    return false;
}

// Bloquear acceso si es dispositivo móvil
if (is_mobile_device()) {
    header('HTTP/1.1 403 Forbidden');
    header('Content-Type: text/html; charset=UTF-8');
    
    // Leer el archivo HTML de acceso denegado
    $html_file = __DIR__ . '/acceso-denegado-movil.html';
    if (file_exists($html_file)) {
        echo file_get_contents($html_file);
    } else {
        // HTML básico si no existe el archivo
        echo '<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Acceso Restringido</title>
    <style>
        body { font-family: Arial, sans-serif; text-align: center; margin-top: 100px; background: #f5f5f5; }
        .container { max-width: 500px; margin: 0 auto; padding: 30px; background: white; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .error { color: #e74c3c; font-size: 20px; margin-bottom: 20px; }
        .message { color: #7f8c8d; line-height: 1.6; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚫 Acceso Restringido</h1>
        <p class="error">El acceso desde dispositivos móviles no está permitido.</p>
        <div class="message">
            <p>Por favor, utilice una computadora, portátil o tablet para acceder al sistema.</p>
            <p><strong>Dispositivos permitidos:</strong></p>
            <ul style="text-align: left; display: inline-block;">
                <li>💻 Computadores de escritorio</li>
                <li>🖥️ Portátiles</li>
                <li>📱 Tablets (iPad, Android Tablets, Surface)</li>
            </ul>
        </div>
        <hr style="margin: 30px 0; border: none; border-top: 1px solid #ecf0f1;">
        <p style="color: #95a5a6; font-size: 14px;">Moodle ICFES - Sistema de Gestión de Aprendizaje</p>
    </div>
</body>
</html>';
    }
    exit;
}
?>
