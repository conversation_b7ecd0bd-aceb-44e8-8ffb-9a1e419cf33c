# Bloqueo de acceso desde dispositivos móviles
# Permite acceso desde tablets, computadores y portátiles
# Bloquea teléfonos móviles

RewriteEngine On

# Detectar dispositivos móviles por User-Agent
RewriteCond %{HTTP_USER_AGENT} "Mobile|Android.*Mobile|iPhone|iPod|BlackBerry|Windows Phone|Opera Mini|IEMobile|webOS|Palm" [NC]

# Permitir tablets específicamente (iPad, Android Tablets, Surface, etc.)
RewriteCond %{HTTP_USER_AGENT} !iPad [NC]
RewriteCond %{HTTP_USER_AGENT} !Tablet [NC]
RewriteCond %{HTTP_USER_AGENT} !"Android(?!.*Mobile)" [NC]
RewriteCond %{HTTP_USER_AGENT} !Surface [NC]

# Redirigir a página de acceso denegado
RewriteRule ^(.*)$ /acceso-denegado-movil.html [R=302,L]
